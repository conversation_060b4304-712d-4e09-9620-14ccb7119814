<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP Client - Tauri Desktop App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar-item:hover {
            transform: translateX(2px);
            transition: transform 0.2s ease;
        }
        .request-tab.active {
            border-bottom: 2px solid #3b82f6;
        }
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
        .json-key { color: #0369a1; }
        .json-string { color: #059669; }
        .json-number { color: #dc2626; }
        .json-boolean { color: #7c3aed; }
        .json-null { color: #6b7280; }
    </style>
</head>
<body class="bg-gray-50 font-sans">
<div id="app" class="flex h-screen">
    <!-- 左侧边栏 -->
    <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
        <!-- 顶部工具栏 -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-xl font-bold text-gray-800">HTTP Client</h1>
                <div class="flex space-x-2">
                    <button @click="showImportModal = true" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <i class="fas fa-file-import"></i>
                    </button>
                    <button @click="exportCollections" class="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <i class="fas fa-file-export"></i>
                    </button>
                </div>
            </div>

            <!-- 环境选择 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">环境</label>
                <div class="flex">
                    <select v-model="currentEnvironment" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">无环境</option>
                        <option v-for="env in environments" :key="env.name" :value="env.name">{{ env.name }}</option>
                    </select>
                    <button @click="showEnvModal = true" class="px-3 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>

            <!-- 新建请求按钮 -->
            <button @click="createNewRequest" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                <i class="fas fa-plus mr-2"></i>
                新建请求
            </button>
        </div>

        <!-- 集合和历史 -->
        <div class="flex-1 overflow-y-auto">
            <div class="p-4">
                <!-- 集合 -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-sm font-semibold text-gray-700 uppercase tracking-wide">集合</h3>
                        <button @click="createCollection" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-plus text-xs"></i>
                        </button>
                    </div>
                    <div v-for="collection in collections" :key="collection.id" class="mb-2">
                        <div class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 cursor-pointer sidebar-item">
                            <div class="flex items-center">
                                <i class="fas fa-folder text-yellow-500 mr-2"></i>
                                <span class="text-sm text-gray-700">{{ collection.name }}</span>
                            </div>
                            <div class="flex space-x-1">
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-edit text-xs"></i>
                                </button>
                                <button class="text-gray-400 hover:text-red-600">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        </div>
                        <div v-for="request in collection.requests" :key="request.id" class="ml-6">
                            <div @click="loadRequest(request)" class="flex items-center p-2 rounded-lg hover:bg-gray-50 cursor-pointer sidebar-item">
                                <span :class="getMethodColor(request.method)" class="text-xs font-bold mr-2">{{ request.method }}</span>
                                <span class="text-sm text-gray-600 truncate">{{ request.name || request.url }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 历史记录 -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-3">历史记录</h3>
                    <div v-for="request in requestHistory" :key="request.id" class="mb-1">
                        <div @click="loadRequest(request)" class="flex items-center p-2 rounded-lg hover:bg-gray-50 cursor-pointer sidebar-item">
                            <span :class="getMethodColor(request.method)" class="text-xs font-bold mr-2">{{ request.method }}</span>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm text-gray-600 truncate">{{ request.url }}</div>
                                <div class="text-xs text-gray-400">{{ formatTime(request.timestamp) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col">
        <!-- 请求配置区域 -->
        <div class="bg-white border-b border-gray-200 p-6">
            <!-- 请求行 -->
            <div class="flex items-center space-x-4 mb-6">
                <select v-model="currentRequest.method" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                    <option value="HEAD">HEAD</option>
                    <option value="OPTIONS">OPTIONS</option>
                </select>
                <div class="flex-1 relative">
                    <input v-model="currentRequest.url" type="text" placeholder="输入请求 URL，支持 {{变量}} 占位符"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div v-if="parsedUrl !== currentRequest.url" class="absolute top-full left-0 right-0 mt-1 p-2 bg-gray-100 rounded-lg text-sm text-gray-600">
                        解析后: {{ parsedUrl }}
                    </div>
                </div>
                <button @click="sendRequest" :disabled="isLoading"
                        class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center">
                    <i v-if="isLoading" class="fas fa-spinner fa-spin mr-2"></i>
                    <i v-else class="fas fa-paper-plane mr-2"></i>
                    {{ isLoading ? '发送中...' : '发送' }}
                </button>
                <button @click="saveRequest" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save"></i>
                </button>
            </div>

            <!-- 请求配置选项卡 -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8">
                    <button v-for="tab in requestTabs" :key="tab"
                            @click="activeRequestTab = tab"
                            :class="['py-2 px-1 border-b-2 font-medium text-sm transition-colors request-tab',
                                        activeRequestTab === tab ? 'border-blue-500 text-blue-600 active' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300']">
                        {{ tab }}
                    </button>
                </nav>
            </div>

            <!-- 请求配置内容 -->
            <div class="mt-4">
                <transition name="fade" mode="out-in">
                    <!-- 查询参数 -->
                    <div v-if="activeRequestTab === '查询参数'" key="params">
                        <div class="space-y-2">
                            <div v-for="(param, index) in currentRequest.params" :key="index" class="flex items-center space-x-2">
                                <input v-model="param.key" placeholder="参数名" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <input v-model="param.value" placeholder="参数值，支持 {{变量}}" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <input v-model="param.description" placeholder="描述" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <label class="flex items-center">
                                    <input v-model="param.enabled" type="checkbox" class="mr-2">
                                    <span class="text-sm text-gray-600">启用</span>
                                </label>
                                <button @click="removeParam(index)" class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <button @click="addParam" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-plus mr-1"></i>添加参数
                            </button>
                        </div>
                    </div>

                    <!-- 请求头 -->
                    <div v-if="activeRequestTab === '请求头'" key="headers">
                        <div class="space-y-2">
                            <div v-for="(header, index) in currentRequest.headers" :key="index" class="flex items-center space-x-2">
                                <input v-model="header.key" placeholder="请求头名称" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <input v-model="header.value" placeholder="请求头值，支持 {{变量}}" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <label class="flex items-center">
                                    <input v-model="header.enabled" type="checkbox" class="mr-2">
                                    <span class="text-sm text-gray-600">启用</span>
                                </label>
                                <button @click="removeHeader(index)" class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <button @click="addHeader" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-plus mr-1"></i>添加请求头
                            </button>
                        </div>
                    </div>

                    <!-- 请求体 -->
                    <div v-if="activeRequestTab === '请求体'" key="body">
                        <div class="mb-4">
                            <div class="flex space-x-4">
                                <label v-for="type in bodyTypes" :key="type" class="flex items-center">
                                    <input v-model="currentRequest.bodyType" :value="type" type="radio" class="mr-2">
                                    <span class="text-sm">{{ type }}</span>
                                </label>
                            </div>
                        </div>

                        <div v-if="currentRequest.bodyType === 'JSON'">
                                <textarea v-model="currentRequest.body.json"
                                          placeholder='{"key": "value", "variable": "{{variable_name}}"}'
                                          class="w-full h-40 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                                          rows="8"></textarea>
                        </div>

                        <div v-else-if="currentRequest.bodyType === 'Form Data'">
                            <div class="space-y-2">
                                <div v-for="(field, index) in currentRequest.body.formData" :key="index" class="flex items-center space-x-2">
                                    <input v-model="field.key" placeholder="字段名" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <div class="flex-1">
                                        <select v-model="field.type" class="mb-2 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                            <option value="text">文本</option>
                                            <option value="file">文件</option>
                                        </select>
                                        <input v-if="field.type === 'text'" v-model="field.value" placeholder="字段值，支持 {{变量}}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <input v-else type="file" @change="handleFileSelect($event, index)" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <label class="flex items-center">
                                        <input v-model="field.enabled" type="checkbox" class="mr-2">
                                        <span class="text-sm text-gray-600">启用</span>
                                    </label>
                                    <button @click="removeFormField(index)" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <button @click="addFormField" class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-plus mr-1"></i>添加字段
                                </button>
                            </div>
                        </div>

                        <div v-else-if="currentRequest.bodyType === 'Text'">
                                <textarea v-model="currentRequest.body.text"
                                          placeholder="输入文本内容，支持 {{变量}} 占位符"
                                          class="w-full h-40 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                                          rows="8"></textarea>
                        </div>
                    </div>

                    <!-- 认证 -->
                    <div v-if="activeRequestTab === '认证'" key="auth">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">认证类型</label>
                            <select v-model="currentRequest.auth.type" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="none">无认证</option>
                                <option value="basic">Basic Auth</option>
                                <option value="bearer">Bearer Token</option>
                            </select>
                        </div>

                        <div v-if="currentRequest.auth.type === 'basic'" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                <input v-model="currentRequest.auth.basic.username" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                <input v-model="currentRequest.auth.basic.password" type="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>

                        <div v-if="currentRequest.auth.type === 'bearer'" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Token</label>
                                <input v-model="currentRequest.auth.bearer.token" type="text" placeholder="支持 {{变量}} 占位符" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>

                    <!-- 脚本 -->
                    <div v-if="activeRequestTab === '脚本'" key="scripts">
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">请求前脚本</label>
                                <textarea v-model="currentRequest.scripts.preRequest"
                                          placeholder="// 请求前执行的 JavaScript 代码&#10;// 可以设置环境变量：pm.environment.set('key', 'value')&#10;// 可以设置全局变量：pm.globals.set('key', 'value')"
                                          class="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                                          rows="6"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">测试脚本</label>
                                <textarea v-model="currentRequest.scripts.test"
                                          placeholder="// 响应后执行的 JavaScript 代码&#10;// 可以访问响应：pm.response.json()&#10;// 可以设置测试：pm.test('Status code is 200', () => { pm.expect(pm.response.code).to.equal(200); })"
                                          class="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                                          rows="6"></textarea>
                            </div>
                        </div>
                    </div>
                </transition>
            </div>
        </div>

        <!-- 响应区域 -->
        <div class="flex-1 bg-white overflow-hidden flex flex-col">
            <div v-if="response" class="flex-1 flex flex-col">
                <!-- 响应状态栏 -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="flex items-center">
                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                <span :class="getStatusColor(response.status)" class="font-bold">{{ response.status }} {{ response.statusText }}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-600 mr-2">时间:</span>
                                <span class="font-medium text-gray-800">{{ response.time }} ms</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-600 mr-2">大小:</span>
                                <span class="font-medium text-gray-800">{{ formatSize(response.size) }}</span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button v-if="canDownload" @click="downloadResponse" class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors">
                                <i class="fas fa-download mr-1"></i>下载
                            </button>
                            <button @click="copyResponse" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                <i class="fas fa-copy mr-1"></i>复制
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 响应选项卡 -->
                <div class="px-6 border-b border-gray-200">
                    <nav class="flex space-x-8">
                        <button v-for="tab in responseTabs" :key="tab"
                                @click="activeResponseTab = tab"
                                :class="['py-3 px-1 border-b-2 font-medium text-sm transition-colors',
                                            activeResponseTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300']">
                            {{ tab }}
                        </button>
                    </nav>
                </div>

                <!-- 响应内容 -->
                <div class="flex-1 overflow-hidden">
                    <transition name="fade" mode="out-in">
                        <!-- 响应体 -->
                        <div v-if="activeResponseTab === '响应体'" key="body" class="h-full p-6 overflow-auto">
                            <div v-if="response.data" class="h-full">
                                <div v-if="isJsonResponse" class="h-full">
                                    <div class="flex justify-between items-center mb-4">
                                        <span class="text-sm text-gray-600">JSON 格式</span>
                                        <button @click="toggleJsonFormat" class="text-blue-600 hover:text-blue-800 text-sm">
                                            {{ jsonFormatted ? '显示原始' : '格式化' }}
                                        </button>
                                    </div>
                                    <pre v-if="jsonFormatted" v-html="formatJson(response.data)" class="bg-gray-50 p-4 rounded-lg text-sm font-mono overflow-auto max-h-96"></pre>
                                    <pre v-else class="bg-gray-50 p-4 rounded-lg text-sm font-mono overflow-auto max-h-96">{{ response.data }}</pre>
                                </div>
                                <div v-else-if="isHtmlResponse" class="h-full">
                                    <div class="flex justify-between items-center mb-4">
                                        <span class="text-sm text-gray-600">HTML 格式</span>
                                        <button @click="toggleHtmlPreview" class="text-blue-600 hover:text-blue-800 text-sm">
                                            {{ htmlPreview ? '显示源码' : '预览' }}
                                        </button>
                                    </div>
                                    <div v-if="htmlPreview" v-html="response.data" class="border rounded-lg p-4 max-h-96 overflow-auto"></div>
                                    <pre v-else class="bg-gray-50 p-4 rounded-lg text-sm font-mono overflow-auto max-h-96">{{ response.data }}</pre>
                                </div>
                                <div v-else>
                                    <span class="text-sm text-gray-600 mb-4 block">文本格式</span>
                                    <pre class="bg-gray-50 p-4 rounded-lg text-sm font-mono overflow-auto max-h-96">{{ response.data }}</pre>
                                </div>
                            </div>
                            <div v-else class="flex items-center justify-center h-full text-gray-500">
                                <div class="text-center">
                                    <i class="fas fa-inbox text-4xl mb-4"></i>
                                    <p>暂无响应数据</p>
                                </div>
                            </div>
                        </div>

                        <!-- 响应头 -->
                        <div v-if="activeResponseTab === '响应头'" key="headers" class="h-full p-6 overflow-auto">
                            <div v-if="response.headers && Object.keys(response.headers).length" class="space-y-2">
                                <div v-for="(value, key) in response.headers" :key="key" class="flex">
                                    <div class="w-1/3 font-medium text-gray-700 break-words">{{ key }}:</div>
                                    <div class="w-2/3 text-gray-600 break-words">{{ value }}</div>
                                </div>
                            </div>
                            <div v-else class="flex items-center justify-center h-full text-gray-500">
                                <div class="text-center">
                                    <i class="fas fa-list text-4xl mb-4"></i>
                                    <p>暂无响应头</p>
                                </div>
                            </div>
                        </div>

                        <!-- 测试结果 -->
                        <div v-if="activeResponseTab === '测试结果'" key="tests" class="h-full p-6 overflow-auto">
                            <div v-if="testResults && testResults.length" class="space-y-3">
                                <div v-for="test in testResults" :key="test.name"
                                     :class="['p-3 rounded-lg border', test.passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200']">
                                    <div class="flex items-center">
                                        <i :class="[test.passed ? 'fas fa-check text-green-600' : 'fas fa-times text-red-600', 'mr-2']"></i>
                                        <span class="font-medium">{{ test.name }}</span>
                                    </div>
                                    <div v-if="test.error" class="mt-2 text-sm text-red-600">{{ test.error }}</div>
                                </div>
                            </div>
                            <div v-else class="flex items-center justify-center h-full text-gray-500">
                                <div class="text-center">
                                    <i class="fas fa-flask text-4xl mb-4"></i>
                                    <p>暂无测试结果</p>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
            <div v-else class="flex-1 flex items-center justify-center text-gray-500">
                <div class="text-center">
                    <i class="fas fa-paper-plane text-6xl mb-6"></i>
                    <h3 class="text-xl font-medium mb-2">发送您的第一个请求</h3>
                    <p>配置好请求参数后，点击发送按钮查看响应结果</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 环境变量模态框 -->
    <div v-if="showEnvModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg w-3/4 max-w-4xl max-h-[80vh] overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold">环境变量管理</h2>
                    <button @click="showEnvModal = false" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="p-6 overflow-auto max-h-[60vh]">
                <div class="flex space-x-6">
                    <!-- 环境列表 -->
                    <div class="w-1/3">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-medium">环境列表</h3>
                            <button @click="addEnvironment" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="space-y-2">
                            <div v-for="env in environments" :key="env.name"
                                 @click="selectedEnv = env"
                                 :class="['p-3 rounded-lg cursor-pointer border', selectedEnv === env ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50 border-gray-200']">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">{{ env.name }}</span>
                                    <button @click.stop="removeEnvironment(env)" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-trash text-xs"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 环境变量编辑 -->
                    <div class="flex-1">
                        <div v-if="selectedEnv">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="font-medium">{{ selectedEnv.name }} - 变量</h3>
                                <button @click="addVariable" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-plus mr-1"></i>添加变量
                                </button>
                            </div>
                            <div class="space-y-2">
                                <div v-for="(variable, index) in selectedEnv.variables" :key="index" class="flex items-center space-x-2">
                                    <input v-model="variable.key" placeholder="变量名" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <input v-model="variable.value" placeholder="变量值" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <button @click="removeVariable(index)" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div v-else class="flex items-center justify-center h-64 text-gray-500">
                            <div class="text-center">
                                <i class="fas fa-cog text-4xl mb-4"></i>
                                <p>选择一个环境来编辑变量</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 flex justify-end">
                <button @click="showEnvModal = false" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div v-if="showImportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg w-96">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold">导入集合</h2>
                    <button @click="showImportModal = false" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                    <input type="file" accept=".json" @change="handleImportFile" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="text-sm text-gray-600">
                    <p>支持的格式:</p>
                    <ul class="list-disc list-inside mt-1">
                        <li>Postman Collection (.json)</li>
                        <li>本应用导出的 JSON 文件</li>
                    </ul>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button @click="showImportModal = false" class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                    取消
                </button>
                <button @click="importCollections" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    导入
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    const { createApp } = Vue;

    createApp({
        data() {
            return {
                // 当前请求
                currentRequest: {
                    id: null,
                    name: '',
                    method: 'GET',
                    url: '',
                    params: [],
                    headers: [],
                    bodyType: 'JSON',
                    body: {
                        json: '',
                        formData: [],
                        text: ''
                    },
                    auth: {
                        type: 'none',
                        basic: { username: '', password: '' },
                        bearer: { token: '' }
                    },
                    scripts: {
                        preRequest: '',
                        test: ''
                    }
                },

                // 响应数据
                response: null,
                testResults: [],
                isLoading: false,

                // 界面状态
                activeRequestTab: '查询参数',
                activeResponseTab: '响应体',
                requestTabs: ['查询参数', '请求头', '请求体', '认证', '脚本'],
                responseTabs: ['响应体', '响应头', '测试结果'],
                bodyTypes: ['JSON', 'Form Data', 'Text'],

                // 数据存储
                collections: [],
                requestHistory: [],
                environments: [],
                currentEnvironment: '',
                selectedEnv: null,
                globalConfig: {
                    headers: [],
                    params: [],
                    variables: []
                },

                // 模态框状态
                showEnvModal: false,
                showImportModal: false,

                // 响应显示控制
                jsonFormatted: true,
                htmlPreview: false,

                // 导入文件
                importFile: null
            };
        },

        computed: {
            parsedUrl() {
                return this.replaceVariables(this.currentRequest.url);
            },

            isJsonResponse() {
                if (!this.response || !this.response.headers) return false;
                const contentType = this.response.headers['content-type'] || this.response.headers['Content-Type'] || '';
                return contentType.includes('application/json') || this.isValidJson(this.response.data);
            },

            isHtmlResponse() {
                if (!this.response || !this.response.headers) return false;
                const contentType = this.response.headers['content-type'] || this.response.headers['Content-Type'] || '';
                return contentType.includes('text/html');
            },

            canDownload() {
                if (!this.response) return false;
                const contentType = this.response.headers['content-type'] || this.response.headers['Content-Type'] || '';
                const contentDisposition = this.response.headers['content-disposition'] || this.response.headers['Content-Disposition'] || '';
                return contentDisposition.includes('attachment') ||
                    contentType.includes('application/') ||
                    contentType.includes('image/') ||
                    contentType.includes('video/') ||
                    contentType.includes('audio/');
            }
        },

        methods: {
            // 请求管理
            createNewRequest() {
                this.currentRequest = {
                    id: null,
                    name: '',
                    method: 'GET',
                    url: '',
                    params: [],
                    headers: [],
                    bodyType: 'JSON',
                    body: {
                        json: '',
                        formData: [],
                        text: ''
                    },
                    auth: {
                        type: 'none',
                        basic: { username: '', password: '' },
                        bearer: { token: '' }
                    },
                    scripts: {
                        preRequest: '',
                        test: ''
                    }
                };
                this.response = null;
                this.testResults = [];
            },

            loadRequest(request) {
                this.currentRequest = JSON.parse(JSON.stringify(request));
                this.response = null;
                this.testResults = [];
            },

            async sendRequest() {
                if (!this.currentRequest.url.trim()) {
                    alert('请输入请求 URL');
                    return;
                }

                this.isLoading = true;
                const startTime = Date.now();

                try {
                    // 执行请求前脚本
                    if (this.currentRequest.scripts.preRequest) {
                        this.executeScript(this.currentRequest.scripts.preRequest);
                    }

                    // 构建请求
                    const requestConfig = this.buildRequestConfig();

                    // 模拟发送请求（实际应用中需要通过 Tauri API 发送）
                    const response = await this.mockHttpRequest(requestConfig);

                    const endTime = Date.now();
                    this.response = {
                        ...response,
                        time: endTime - startTime,
                        size: JSON.stringify(response.data).length
                    };

                    // 执行测试脚本
                    if (this.currentRequest.scripts.test) {
                        this.testResults = this.executeTestScript(this.currentRequest.scripts.test);
                    }

                    // 添加到历史记录
                    this.addToHistory();

                } catch (error) {
                    this.response = {
                        status: 0,
                        statusText: 'Network Error',
                        headers: {},
                        data: error.message,
                        time: Date.now() - startTime,
                        size: 0
                    };
                } finally {
                    this.isLoading = false;
                }
            },

            buildRequestConfig() {
                const config = {
                    method: this.currentRequest.method,
                    url: this.replaceVariables(this.currentRequest.url),
                    headers: {},
                    params: {}
                };

                // 添加查询参数
                this.currentRequest.params.forEach(param => {
                    if (param.enabled && param.key) {
                        config.params[param.key] = this.replaceVariables(param.value);
                    }
                });

                // 添加请求头
                this.currentRequest.headers.forEach(header => {
                    if (header.enabled && header.key) {
                        config.headers[header.key] = this.replaceVariables(header.value);
                    }
                });

                // 处理认证
                if (this.currentRequest.auth.type === 'basic') {
                    const { username, password } = this.currentRequest.auth.basic;
                    config.headers['Authorization'] = `Basic ${btoa(username + ':' + password)}`;
                } else if (this.currentRequest.auth.type === 'bearer') {
                    const token = this.replaceVariables(this.currentRequest.auth.bearer.token);
                    config.headers['Authorization'] = `Bearer ${token}`;
                }

                // 处理请求体
                if (['POST', 'PUT', 'PATCH'].includes(this.currentRequest.method)) {
                    if (this.currentRequest.bodyType === 'JSON') {
                        config.headers['Content-Type'] = 'application/json';
                        config.data = this.replaceVariables(this.currentRequest.body.json);
                    } else if (this.currentRequest.bodyType === 'Form Data') {
                        const formData = new FormData();
                        this.currentRequest.body.formData.forEach(field => {
                            if (field.enabled && field.key) {
                                if (field.type === 'file' && field.file) {
                                    formData.append(field.key, field.file);
                                } else {
                                    formData.append(field.key, this.replaceVariables(field.value));
                                }
                            }
                        });
                        config.data = formData;
                    } else if (this.currentRequest.bodyType === 'Text') {
                        config.headers['Content-Type'] = 'text/plain';
                        config.data = this.replaceVariables(this.currentRequest.body.text);
                    }
                }

                return config;
            },

            // 模拟 HTTP 请求（实际应用中应该通过 Tauri 后端调用）
            async mockHttpRequest(config) {
                // 模拟延迟
                await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

                // 模拟响应
                const mockResponse = {
                    status: 200,
                    statusText: 'OK',
                    headers: {
                        'Content-Type': 'application/json',
                        'Server': 'nginx/1.18.0',
                        'Date': new Date().toUTCString()
                    },
                    data: JSON.stringify({
                        method: config.method,
                        url: config.url,
                        params: config.params,
                        headers: config.headers,
                        timestamp: new Date().toISOString(),
                        message: '这是一个模拟响应'
                    }, null, 2)
                };

                return mockResponse;
            },

            saveRequest() {
                const name = prompt('请输入请求名称:');
                if (!name) return;

                const request = {
                    ...JSON.parse(JSON.stringify(this.currentRequest)),
                    id: Date.now().toString(),
                    name: name,
                    timestamp: Date.now()
                };

                // 简单保存到第一个集合，实际应用中应该让用户选择
                if (this.collections.length === 0) {
                    this.createCollection();
                }

                this.collections[0].requests.push(request);
                this.saveToLocalStorage();
                alert('请求已保存');
            },

            addToHistory() {
                const historyItem = {
                    ...JSON.parse(JSON.stringify(this.currentRequest)),
                    id: Date.now().toString(),
                    timestamp: Date.now()
                };

                this.requestHistory.unshift(historyItem);
                if (this.requestHistory.length > 50) {
                    this.requestHistory = this.requestHistory.slice(0, 50);
                }

                this.saveToLocalStorage();
            },

            // 参数管理
            addParam() {
                this.currentRequest.params.push({ key: '', value: '', description: '', enabled: true });
            },

            removeParam(index) {
                this.currentRequest.params.splice(index, 1);
            },

            addHeader() {
                this.currentRequest.headers.push({ key: '', value: '', enabled: true });
            },

            removeHeader(index) {
                this.currentRequest.headers.splice(index, 1);
            },

            addFormField() {
                this.currentRequest.body.formData.push({ key: '', value: '', type: 'text', enabled: true });
            },

            removeFormField(index) {
                this.currentRequest.body.formData.splice(index, 1);
            },

            handleFileSelect(event, index) {
                const file = event.target.files[0];
                if (file) {
                    this.currentRequest.body.formData[index].file = file;
                    this.currentRequest.body.formData[index].value = file.name;
                }
            },

            // 集合管理
            createCollection() {
                const name = prompt('请输入集合名称:');
                if (!name) return;

                const collection = {
                    id: Date.now().toString(),
                    name: name,
                    requests: []
                };

                this.collections.push(collection);
                this.saveToLocalStorage();
            },

            // 环境变量管理
            addEnvironment() {
                const name = prompt('请输入环境名称:');
                if (!name) return;

                const env = {
                    name: name,
                    variables: []
                };

                this.environments.push(env);
                this.selectedEnv = env;
                this.saveToLocalStorage();
            },

            removeEnvironment(env) {
                if (confirm(`确定要删除环境 "${env.name}" 吗？`)) {
                    const index = this.environments.indexOf(env);
                    this.environments.splice(index, 1);
                    if (this.selectedEnv === env) {
                        this.selectedEnv = null;
                    }
                    if (this.currentEnvironment === env.name) {
                        this.currentEnvironment = '';
                    }
                    this.saveToLocalStorage();
                }
            },

            addVariable() {
                if (this.selectedEnv) {
                    this.selectedEnv.variables.push({ key: '', value: '' });
                }
            },

            removeVariable(index) {
                if (this.selectedEnv) {
                    this.selectedEnv.variables.splice(index, 1);
                }
            },

            // 变量替换
            replaceVariables(text) {
                if (!text) return text;

                let result = text;
                const env = this.environments.find(e => e.name === this.currentEnvironment);

                if (env) {
                    env.variables.forEach(variable => {
                        const regex = new RegExp(`{{\\s*${variable.key}\\s*}}`, 'g');
                        result = result.replace(regex, variable.value);
                    });
                }

                return result;
            },

            // 脚本执行
            executeScript(script) {
                // 创建简单的脚本执行环境
                const pm = {
                    environment: {
                        set: (key, value) => {
                            const env = this.environments.find(e => e.name === this.currentEnvironment);
                            if (env) {
                                const variable = env.variables.find(v => v.key === key);
                                if (variable) {
                                    variable.value = value;
                                } else {
                                    env.variables.push({ key, value });
                                }
                            }
                        },
                        get: (key) => {
                            const env = this.environments.find(e => e.name === this.currentEnvironment);
                            if (env) {
                                const variable = env.variables.find(v => v.key === key);
                                return variable ? variable.value : undefined;
                            }
                            return undefined;
                        }
                    },
                    globals: {
                        set: (key, value) => {
                            const variable = this.globalConfig.variables.find(v => v.key === key);
                            if (variable) {
                                variable.value = value;
                            } else {
                                this.globalConfig.variables.push({ key, value });
                            }
                        },
                        get: (key) => {
                            const variable = this.globalConfig.variables.find(v => v.key === key);
                            return variable ? variable.value : undefined;
                        }
                    }
                };

                try {
                    // 简单的脚本执行（实际应用中需要更安全的执行环境）
                    const func = new Function('pm', script);
                    func(pm);
                } catch (error) {
                    console.error('Script execution error:', error);
                }
            },

            executeTestScript(script) {
                const tests = [];

                const pm = {
                    response: {
                        code: this.response.status,
                        status: this.response.statusText,
                        headers: this.response.headers,
                        text: () => this.response.data,
                        json: () => {
                            try {
                                return JSON.parse(this.response.data);
                            } catch (e) {
                                throw new Error('Response is not valid JSON');
                            }
                        }
                    },
                    test: (name, testFunc) => {
                        try {
                            testFunc();
                            tests.push({ name, passed: true });
                        } catch (error) {
                            tests.push({ name, passed: false, error: error.message });
                        }
                    },
                    expect: (actual) => ({
                        to: {
                            equal: (expected) => {
                                if (actual !== expected) {
                                    throw new Error(`Expected ${expected} but got ${actual}`);
                                }
                            },
                            include: (expected) => {
                                if (!actual.includes(expected)) {
                                    throw new Error(`Expected ${actual} to include ${expected}`);
                                }
                            }
                        }
                    })
                };

                try {
                    const func = new Function('pm', script);
                    func(pm);
                } catch (error) {
                    tests.push({ name: '脚本执行错误', passed: false, error: error.message });
                }

                return tests;
            },

            // 响应处理
            formatJson(jsonString) {
                try {
                    const obj = JSON.parse(jsonString);
                    return this.syntaxHighlight(JSON.stringify(obj, null, 2));
                } catch (e) {
                    return jsonString;
                }
            },

            syntaxHighlight(json) {
                json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                    var cls = 'json-number';
                    if (/^"/.test(match)) {
                        if (/:$/.test(match)) {
                            cls = 'json-key';
                        } else {
                            cls = 'json-string';
                        }
                    } else if (/true|false/.test(match)) {
                        cls = 'json-boolean';
                    } else if (/null/.test(match)) {
                        cls = 'json-null';
                    }
                    return '<span class="' + cls + '">' + match + '</span>';
                });
            },

            isValidJson(str) {
                try {
                    JSON.parse(str);
                    return true;
                } catch (e) {
                    return false;
                }
            },

            toggleJsonFormat() {
                this.jsonFormatted = !this.jsonFormatted;
            },

            toggleHtmlPreview() {
                this.htmlPreview = !this.htmlPreview;
            },

            downloadResponse() {
                if (!this.response || !this.response.data) return;

                const contentDisposition = this.response.headers['content-disposition'] || this.response.headers['Content-Disposition'] || '';
                const contentType = this.response.headers['content-type'] || this.response.headers['Content-Type'] || 'application/octet-stream';

                let filename = 'response';
                const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (filenameMatch) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                } else {
                    // 根据 Content-Type 推断文件扩展名
                    if (contentType.includes('json')) filename += '.json';
                    else if (contentType.includes('xml')) filename += '.xml';
                    else if (contentType.includes('html')) filename += '.html';
                    else if (contentType.includes('text')) filename += '.txt';
                    else if (contentType.includes('pdf')) filename += '.pdf';
                    else if (contentType.includes('image')) filename += '.img';
                }

                const blob = new Blob([this.response.data], { type: contentType });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            },

            copyResponse() {
                if (!this.response || !this.response.data) return;

                navigator.clipboard.writeText(this.response.data).then(() => {
                    alert('响应内容已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            },

            // 导入导出
            exportCollections() {
                const data = {
                    collections: this.collections,
                    environments: this.environments,
                    globalConfig: this.globalConfig,
                    exportedAt: new Date().toISOString(),
                    version: '1.0'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `http-client-export-${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            },

            handleImportFile(event) {
                this.importFile = event.target.files[0];
            },

            async importCollections() {
                if (!this.importFile) {
                    alert('请选择要导入的文件');
                    return;
                }

                try {
                    const text = await this.importFile.text();
                    const data = JSON.parse(text);

                    // 检查是否是本应用导出的格式
                    if (data.version && data.collections) {
                        this.collections = [...this.collections, ...data.collections];
                        if (data.environments) {
                            this.environments = [...this.environments, ...data.environments];
                        }
                        if (data.globalConfig) {
                            this.globalConfig = { ...this.globalConfig, ...data.globalConfig };
                        }
                    } else {
                        // 尝试解析 Postman 格式（简化版）
                        if (data.info && data.item) {
                            const collection = {
                                id: Date.now().toString(),
                                name: data.info.name || 'Imported Collection',
                                requests: this.parsePostmanItems(data.item)
                            };
                            this.collections.push(collection);
                        }
                    }

                    this.saveToLocalStorage();
                    this.showImportModal = false;
                    alert('导入成功');

                } catch (error) {
                    alert('导入失败: